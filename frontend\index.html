<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Doctor.Ai - AI-Powered Resume Scanner & Job Matching Platform</title>
    <link rel="stylesheet" href="/static/css/us10_styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                🤖 Welcome to Resume Doctor.Ai
            </h1>
            <p class="subtitle">AI-Powered Resume Scanner & Job Matching Platform</p>
        </div>
        
        <div class="content">
            <p class="welcome-text">
                Get started by creating an account or signing in to access all features.
            </p>
            
            <a href="us10_register.html" class="btn btn-primary">
                👤 Create Account
            </a>
            
            <a href="us10_login.html" class="btn btn-secondary">
                🔐 Sign In
            </a>
        </div>
    </div>

    <script src="/static/js/config.js"></script>
    <script>
        // Check if user is already logged in
        const token = localStorage.getItem('dr_resume_token');
        if (token) {
            // Redirect to dashboard if already logged in
            window.location.href = 'us10_dashboard.html';
        }
        
        // Simple page analytics
        console.log('🤖 Resume Doctor.Ai Landing Page Loaded (US-05)');
        console.log('🔍 US-05: Keyword Parsing & Extraction System');
        
        // Add click tracking for buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                console.log(`Button clicked: ${this.textContent.trim()}`);
            });
        });
    </script>
</body>
</html>

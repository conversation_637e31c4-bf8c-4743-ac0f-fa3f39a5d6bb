<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Settings - Resume Doctor.Ai</title>
    <link rel="stylesheet" href="/static/css/us10_styles.css">
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div>
                <h1>🩺 Resume Doctor.Ai</h1>
                <nav class="dashboard-nav">
                    <a href="/dashboard" class="nav-item">📊 Dashboard</a>
                    <a href="#" class="nav-item active">⚙️ Account</a>
                </nav>
            </div>
            
            <div class="user-info">
                <span id="welcomeMessage">Welcome, GUEST</span>
                <button class="logout-btn" onclick="logout()">🚪 Logout</button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <h2 style="margin-bottom: 30px; color: #1f2937;">⚙️ Account Settings</h2>
            
            <!-- Alert Container -->
            <div id="alertContainer"></div>
            
            <!-- Account Information Section -->
            <div class="settings-section">
                <h3 class="section-title">👤 Personal Information</h3>
                <div class="settings-card">
                    <form id="personalInfoForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">First Name</label>
                                <input type="text" id="firstName" name="first_name" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name</label>
                                <input type="text" id="lastName" name="last_name" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="currentPasswordInfo">Current Password (required for email changes)</label>
                            <input type="password" id="currentPasswordInfo" name="current_password" placeholder="Enter current password to save changes">
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="resetPersonalInfo()">Reset</button>
                            <button type="submit" class="btn-primary">💾 Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Password Change Section -->
            <div class="settings-section">
                <h3 class="section-title">🔐 Change Password</h3>
                <div class="settings-card">
                    <form id="passwordForm">
                        <div class="form-group">
                            <label for="currentPassword">Current Password</label>
                            <input type="password" id="currentPassword" name="current_password" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="newPassword">New Password</label>
                            <input type="password" id="newPassword" name="new_password" required>
                            <small class="form-help">Password must be at least 8 characters long</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">Confirm New Password</label>
                            <input type="password" id="confirmPassword" name="confirm_password" required>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="resetPasswordForm()">Reset</button>
                            <button type="submit" class="btn-primary">🔐 Change Password</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Account Status Section -->
            <div class="settings-section">
                <h3 class="section-title">📊 Account Status</h3>
                <div class="settings-card">
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-label">Account Type</div>
                            <div class="status-value" id="accountType">Basic</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">Member Since</div>
                            <div class="status-value" id="memberSince">-</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">Last Login</div>
                            <div class="status-value" id="lastLogin">-</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">Total Resumes</div>
                            <div class="status-value" id="totalResumes">0</div>
                        </div>
                    </div>
                    
                    <div class="upgrade-section" id="upgradeSection" style="display: none;">
                        <h4>🌟 Upgrade to Premium</h4>
                        <p>Get access to AI-powered suggestions and advanced features!</p>
                        <button class="btn-premium" onclick="upgradeToPremium()">⭐ Upgrade Now</button>
                    </div>
                </div>
            </div>
            
            <!-- Danger Zone -->
            <div class="settings-section danger-zone">
                <h3 class="section-title">⚠️ Danger Zone</h3>
                <div class="settings-card">
                    <div class="danger-content">
                        <h4>Delete Account</h4>
                        <p>Once you delete your account, there is no going back. Please be certain.</p>
                        <button class="btn-danger" onclick="showDeleteConfirmation()">🗑️ Delete Account</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚠️ Confirm Account Deletion</h3>
                <button class="modal-close" onclick="hideDeleteConfirmation()">&times;</button>
            </div>
            <div class="modal-body">
                <p><strong>This action cannot be undone.</strong></p>
                <p>Your account and all associated data will be permanently deleted.</p>
                
                <form id="deleteForm">
                    <div class="form-group">
                        <label for="deletePassword">Enter your password to confirm:</label>
                        <input type="password" id="deletePassword" name="password" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="confirmDeletion" name="confirm_deletion" required>
                            I understand that this action cannot be undone
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="hideDeleteConfirmation()">Cancel</button>
                <button class="btn-danger" onclick="deleteAccount()">🗑️ Delete Account</button>
            </div>
        </div>
    </div>

    <script src="/static/js/config.js"></script>
    <script src="/static/js/us10_account.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Resume Doctor.Ai</title>
    <link rel="stylesheet" href="./static/css/us10_styles.css">
    <style>
        /* Inline auth-specific styles for file:// protocol compatibility */
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 100%);
            background-attachment: fixed;
        }

        body::before {
            background:
                radial-gradient(circle at 20% 80%, rgba(71, 85, 105, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(100, 116, 139, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(148, 163, 184, 0.04) 0%, transparent 50%);
        }

        .auth-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 16px;
            padding: 40px;
            width: 100%;
            max-width: 420px;
            box-shadow:
                0 10px 25px rgba(71, 85, 105, 0.15),
                0 4px 10px rgba(71, 85, 105, 0.1),
                0 0 0 1px rgba(148, 163, 184, 0.1);
            position: relative;
            overflow: hidden;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(71, 85, 105, 0.2), transparent);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .auth-header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .auth-header .subtitle {
            font-size: 14px;
            color: #6b7280;
            font-weight: 400;
        }

        .auth-footer {
            text-align: center;
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }

        .auth-link {
            color: #475569;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .auth-link:hover {
            color: #334155;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container auth-container">
        <div class="auth-header">
            <h1>🔐 Login to Resume Doctor.Ai</h1>
            <p class="subtitle">AI-Powered Resume Analysis & Job Matching Platform</p>
        </div>

        <div id="alertContainer"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" class="form-control" placeholder="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" placeholder="••••••••" required>
            </div>

            <button type="submit" class="btn btn-primary" id="loginBtn">Login</button>
        </form>

        <div class="auth-footer">
            Don't have an account? <a href="us10_register.html" class="auth-link">Register here</a>
        </div>
    </div>

    <script src="./static/js/config.js"></script>
    <script src="./static/js/us10_login.js"></script>
</body>
</html>

/* Resume Doctor.Ai - Professional Lightweight Design */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 100%);
    background-attachment: fixed;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #334155;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(71, 85, 105, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(100, 116, 139, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(148, 163, 184, 0.04) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow:
        0 10px 25px rgba(71, 85, 105, 0.15),
        0 4px 10px rgba(71, 85, 105, 0.1),
        0 0 0 1px rgba(148, 163, 184, 0.1);
    overflow: hidden;
    width: 100%;
    max-width: 800px;
    border: 1px solid rgba(148, 163, 184, 0.2);
}

.header {
    background: linear-gradient(135deg, #475569 0%, #64748b 50%, #94a3b8 100%);
    color: white;
    padding: 32px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(148, 163, 184, 0.1) 0%, rgba(203, 213, 225, 0.05) 100%);
    pointer-events: none;
}

.header h1 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.header .subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
}

.content {
    padding: 40px 30px;
}

.welcome-text {
    text-align: center;
    color: #6b7280;
    margin-bottom: 30px;
    font-size: 16px;
    line-height: 1.5;
}

.btn {
    width: 100%;
    padding: 16px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    margin-bottom: 12px;
}

.btn-primary {
    background: linear-gradient(135deg, #475569 0%, #64748b 50%, #94a3b8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(71, 85, 105, 0.25);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(71, 85, 105, 0.35);
    background: linear-gradient(135deg, #64748b 0%, #94a3b8 50%, #cbd5e1 100%);
}

.btn-secondary {
    background: transparent;
    color: #475569;
    border: 2px solid #cbd5e1;
}

.btn-secondary:hover {
    background: #475569;
    color: white;
    border-color: #475569;
    transform: translateY(-2px);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s ease;
    background: #f9fafb;
}

.form-control:focus {
    outline: none;
    border-color: #475569;
    background: white;
    box-shadow: 0 0 0 3px rgba(71, 85, 105, 0.1);
}

/* File Upload Styles */
.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    background: #f9fafb;
    transition: all 0.2s ease;
    cursor: pointer;
    margin-bottom: 20px;
}

.upload-area:hover {
    border-color: #475569;
    background: #f1f5f9;
}

.upload-area.dragover {
    border-color: #475569;
    background: #e2e8f0;
}

.upload-icon {
    font-size: 48px;
    color: #9ca3af;
    margin-bottom: 16px;
}

.upload-text {
    color: #6b7280;
    margin-bottom: 8px;
}

.upload-subtext {
    color: #9ca3af;
    font-size: 14px;
}

.file-input {
    display: none;
}

.file-info {
    background: #f3f4f6;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    display: none;
}

.file-name {
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
}

.file-size {
    color: #6b7280;
    font-size: 14px;
}

/* Progress Bar */
.progress-container {
    margin: 20px 0;
    display: none;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #475569 0%, #64748b 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    margin-top: 8px;
    color: #6b7280;
    font-size: 14px;
}

/* Textarea Styles */
.textarea-container {
    position: relative;
}

.form-textarea {
    width: 100%;
    min-height: 300px;
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    font-family: inherit;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
    background: #f9fafb;
}

.form-textarea:focus {
    outline: none;
    border-color: #475569;
    background: white;
    box-shadow: 0 0 0 3px rgba(71, 85, 105, 0.1);
}

.character-counter {
    position: absolute;
    bottom: 12px;
    right: 16px;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #6b7280;
    border: 1px solid #e5e7eb;
}

.character-counter.warning {
    color: #f59e0b;
    border-color: #f59e0b;
}

.character-counter.error {
    color: #ef4444;
    border-color: #ef4444;
}

/* Alert Messages */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

/* Dashboard Styles */
.dashboard-container {
    max-width: 1200px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 16px;
    box-shadow:
        0 10px 25px rgba(71, 85, 105, 0.15),
        0 4px 10px rgba(71, 85, 105, 0.1),
        0 0 0 1px rgba(148, 163, 184, 0.1);
    overflow: hidden;
    border: 1px solid rgba(148, 163, 184, 0.2);
}

.dashboard-header {
    background: linear-gradient(135deg, #475569 0%, #64748b 50%, #94a3b8 100%);
    color: white;
    padding: 24px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-nav {
    display: flex;
    gap: 20px;
    align-items: center;
}

.nav-item {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.2);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.dashboard-content {
    padding: 40px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(71, 85, 105, 0.08);
}

.dashboard-card:hover {
    border-color: #475569;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(71, 85, 105, 0.15);
}

.card-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.card-description {
    color: #6b7280;
    margin-bottom: 20px;
    line-height: 1.5;
}

.card-button {
    background: #475569;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.card-button:hover {
    background: #334155;
}

/* Job Description List Styles */
.jd-list {
    margin-top: 30px;
}

.jd-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    transition: all 0.2s ease;
}

.jd-item:hover {
    border-color: #475569;
    box-shadow: 0 4px 12px rgba(71, 85, 105, 0.1);
}

.jd-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.jd-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 18px;
    margin-bottom: 4px;
}

.jd-company {
    color: #475569;
    font-weight: 500;
    margin-bottom: 8px;
}

.jd-meta {
    color: #6b7280;
    font-size: 14px;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.jd-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.btn-small {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-view {
    background: #3b82f6;
    color: white;
}

.btn-view:hover {
    background: #2563eb;
}

.btn-edit {
    background: #10b981;
    color: white;
}

.btn-edit:hover {
    background: #059669;
}

.btn-delete {
    background: #ef4444;
    color: white;
}

.btn-delete:hover {
    background: #dc2626;
}

/* Loading State */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .dashboard-nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .jd-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .jd-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .container {
        max-width: none;
        margin: 10px;
    }
}

@media (max-width: 480px) {
    .container, .dashboard-container {
        margin: 10px;
        max-width: none;
    }
    
    .header, .dashboard-content {
        padding: 30px 20px;
    }
    
    .content {
        padding: 30px 20px;
    }
    
    .header h1 {
        font-size: 24px;
    }
    
    .form-textarea {
        min-height: 250px;
    }
}

/* Keyword Analysis Styles (NEW for US-05) */
.keyword-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
}

.keyword-section h3 {
    color: #1f2937;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.keyword-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.keyword-tag {
    background: #f3f4f6;
    color: #374151;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.keyword-tag.technical {
    background: #dbeafe;
    color: #1e40af;
    border-color: #93c5fd;
}

.keyword-tag.soft {
    background: #dcfce7;
    color: #166534;
    border-color: #86efac;
}

.keyword-tag.other {
    background: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
}

.keyword-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.keyword-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.keyword-stat {
    text-align: center;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
}

.keyword-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #475569;
    margin-bottom: 4px;
}

.keyword-stat-label {
    color: #6b7280;
    font-size: 14px;
}

.parse-button {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.parse-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.parse-button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.parse-all-section {
    background: #f8fafc;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    text-align: center;
}

.parse-all-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 16px;
}

.keyword-empty {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 24px;
}

.keyword-analysis-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.2s ease;
}

.keyword-analysis-card:hover {
    border-color: #475569;
    box-shadow: 0 4px 12px rgba(71, 85, 105, 0.1);
}

.keyword-analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.keyword-analysis-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.keyword-analysis-meta {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 8px;
}

.keyword-analysis-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.keyword-count-badge {
    background: #475569;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.no-keywords {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

/* US-06: Matching Score Styles */
.main-content {
    padding: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.match-calculator {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.match-calculator h2 {
    color: #1f2937;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.calculator-form {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 20px;
    align-items: end;
}

.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    background: white;
    transition: border-color 0.2s ease;
}

.form-select:focus {
    outline: none;
    border-color: #475569;
    box-shadow: 0 0 0 3px rgba(71, 85, 105, 0.1);
}

.loading-section {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 12px;
    margin-bottom: 30px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #475569;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

.match-result {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.score-display {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 40px;
    align-items: center;
    margin-bottom: 30px;
}

.overall-score {
    text-align: center;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(#475569 0deg, #64748b 180deg, #e5e7eb 180deg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 auto;
}

.score-circle::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
}

.score-value {
    font-size: 24px;
    font-weight: bold;
    color: #1f2937;
    z-index: 1;
}

.score-label {
    font-size: 12px;
    color: #6b7280;
    z-index: 1;
}

.detailed-scores {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.score-item {
    display: grid;
    grid-template-columns: 150px 1fr auto;
    gap: 16px;
    align-items: center;
}

.score-category {
    font-weight: 500;
    color: #374151;
}

.progress-bar {
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #475569 0%, #64748b 100%);
    transition: width 0.5s ease;
    border-radius: 4px;
}

.score-percentage {
    font-weight: 600;
    color: #1f2937;
    min-width: 50px;
    text-align: right;
}

.match-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    background: #f8fafc;
    padding: 20px;
    border-radius: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    color: #6b7280;
    font-size: 14px;
}

.detail-value {
    font-weight: 600;
    color: #1f2937;
}

.match-history {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
}

.match-history h2 {
    color: #1f2937;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.history-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.history-item {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
}

.history-item:hover {
    border-color: #475569;
    box-shadow: 0 2px 8px rgba(71, 85, 105, 0.1);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.history-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.history-meta {
    color: #6b7280;
    font-size: 14px;
}

.history-score {
    text-align: right;
}

.history-score-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
}

.history-score-value.excellent {
    color: #10b981;
}

.history-score-value.good {
    color: #3b82f6;
}

.history-score-value.fair {
    color: #f59e0b;
}

.history-score-value.poor {
    color: #ef4444;
}

.history-score-label {
    font-size: 12px;
    color: #6b7280;
}

.loading-text {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 40px;
}

.error-message, .success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 20px;
    border-radius: 8px;
    font-weight: 500;
    z-index: 1000;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-message {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.success-message {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

/* Navigation Styles */
.nav {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-top: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Responsive Design for Matching */
@media (max-width: 768px) {
    .calculator-form {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .score-display {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .score-item {
        grid-template-columns: 1fr;
        gap: 8px;
        text-align: center;
    }

    .match-details {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .detail-item {
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }

    .history-header {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .nav {
        gap: 8px;
    }

    .nav-link {
        padding: 6px 12px;
        font-size: 13px;
    }
}

/* US-07: Suggestions Page Styles */
.suggestion-calculator {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.suggestion-calculator h2 {
    color: #1f2937;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.suggestion-buttons {
    display: flex;
    gap: 16px;
    margin-top: 20px;
}

.btn-premium {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(100, 116, 139, 0.3);
}

.btn-premium:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.suggestions-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.premium-section {
    border: 2px solid #64748b;
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f3f4f6;
}

.suggestion-type-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.suggestion-type-badge.basic {
    background: #dbeafe;
    color: #1e40af;
}

.suggestion-type-badge.premium {
    background: #ede9fe;
    color: #475569;
}

/* Missing Keywords Styles */
.missing-keywords-container {
    margin-bottom: 30px;
}

.keyword-categories {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.keyword-category h4 {
    color: #374151;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.keyword-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    min-height: 40px;
    padding: 8px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px dashed #d1d5db;
}

.keyword-pill {
    background: #ef4444;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.keyword-pill:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.keyword-pill.technical {
    background: #3b82f6;
}

.keyword-pill.soft {
    background: #10b981;
}

.keyword-pill.other {
    background: #f59e0b;
}

/* Matched vs Missing keyword styles */
.keyword-pills.matched .keyword-pill {
    background: #10b981;
    border: 2px solid #059669;
}

.keyword-pills.matched .keyword-pill.technical {
    background: #3b82f6;
    border: 2px solid #2563eb;
}

.keyword-pills.matched .keyword-pill.soft {
    background: #10b981;
    border: 2px solid #059669;
}

.keyword-pills.matched .keyword-pill.other {
    background: #64748b;
    border: 2px solid #475569;
}

.keyword-pills.missing .keyword-pill {
    background: #ef4444;
    border: 2px solid #dc2626;
}

.keyword-pills.missing .keyword-pill.technical {
    background: #ef4444;
    border: 2px solid #dc2626;
}

.keyword-pills.missing .keyword-pill.soft {
    background: #f97316;
    border: 2px solid #ea580c;
}

.keyword-pills.missing .keyword-pill.other {
    background: #6b7280;
    border: 2px solid #4b5563;
}

/* Keyword analysis container */
.keyword-analysis-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.matched-keywords-section,
.missing-keywords-section {
    margin-bottom: 20px;
}

.matched-keywords-section h4 {
    color: #059669;
    margin-bottom: 15px;
}

.missing-keywords-section h4 {
    color: #dc2626;
    margin-bottom: 15px;
}

.empty-keywords {
    color: #6b7280;
    font-style: italic;
    text-align: center;
    padding: 16px;
}

/* Recommendations Styles */
.recommendations-container {
    margin-bottom: 30px;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.recommendation-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
}

.recommendation-item:hover {
    border-color: #475569;
    box-shadow: 0 2px 8px rgba(71, 85, 105, 0.1);
}

.recommendation-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.recommendation-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.recommendation-priority {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.recommendation-priority.high {
    background: #fee2e2;
    color: #991b1b;
}

.recommendation-priority.medium {
    background: #fef3c7;
    color: #92400e;
}

.recommendation-priority.low {
    background: #d1fae5;
    color: #065f46;
}

.recommendation-description {
    color: #6b7280;
    margin-bottom: 12px;
    line-height: 1.5;
}

.recommendation-action {
    color: #475569;
    font-weight: 500;
    font-size: 14px;
}

/* Priority Analysis Styles */
.priority-analysis {
    background: #f8fafc;
    border-radius: 8px;
    padding: 20px;
}

.priority-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.priority-item {
    text-align: center;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.priority-count {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
}

.priority-count.high {
    color: #ef4444;
}

.priority-count.medium {
    color: #f59e0b;
}

.priority-count.low {
    color: #10b981;
}

.priority-label {
    color: #6b7280;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 500;
}

/* Premium Section Styles */
.premium-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.ai-summary {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #0ea5e9;
}

.ai-summary-content {
    color: #0f172a;
    line-height: 1.6;
    font-size: 16px;
}

.ai-recommendations {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #22c55e;
}

.structure-improvements {
    background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #eab308;
}

.ats-optimizations {
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #ec4899;
}

.quick-wins {
    background: linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #64748b;
}

.ai-recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 12px;
}

.ai-recommendation-item {
    background: rgba(255, 255, 255, 0.7);
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #374151;
    line-height: 1.5;
}

/* Premium Upgrade CTA */
.premium-upgrade-cta {
    text-align: center;
    padding: 40px;
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
    border-radius: 12px;
    border: 2px dashed #64748b;
}

.upgrade-content h3 {
    color: #475569;
    margin-bottom: 16px;
}

.upgrade-content p {
    color: #6b7280;
    margin-bottom: 24px;
    line-height: 1.6;
}

.premium-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 24px;
}

.feature {
    color: #475569;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-premium-upgrade {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-premium-upgrade:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(100, 116, 139, 0.3);
}

/* Responsive Design for Suggestions */
@media (max-width: 768px) {
    .suggestion-buttons {
        flex-direction: column;
        gap: 12px;
    }

    .keyword-categories {
        gap: 16px;
    }

    .priority-breakdown {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .premium-features {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .section-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
}

/* US-10: Scan History Styles */
.scan-history-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.scan-history-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.score-badge {
    font-weight: bold;
    text-align: center;
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 14px;
    display: inline-block;
}

.score-excellent {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.score-good {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.score-fair {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.score-poor {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

/* Pagination Styles */
#historyPagination button {
    transition: all 0.2s ease;
}

#historyPagination button:hover:not(:disabled) {
    background: #475569 !important;
    color: white !important;
    transform: translateY(-1px);
}

#historyPagination button:disabled {
    cursor: not-allowed !important;
    opacity: 0.5 !important;
}

/* Filter Dropdown */
#scoreFilter {
    transition: border-color 0.2s ease;
}

#scoreFilter:focus {
    outline: none;
    border-color: #475569;
    box-shadow: 0 0 0 3px rgba(71, 85, 105, 0.1);
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    animation: spin 1s linear infinite;
    display: inline-block;
}

/* Responsive Design for Scan History */
@media (max-width: 768px) {
    .scan-history-card {
        padding: 15px !important;
    }

    .scan-history-card > div:first-child {
        flex-direction: column !important;
        gap: 15px !important;
    }

    .scan-history-card .score-badge {
        align-self: flex-start;
    }

    .scan-history-card > div:last-child {
        flex-direction: column !important;
        gap: 8px !important;
    }

    .scan-history-card button {
        width: 100% !important;
        text-align: center !important;
    }
}

/* US-10: Account Settings Styles */
.settings-section {
    margin-bottom: 30px;
}

.section-title {
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #475569;
    box-shadow: 0 0 0 3px rgba(71, 85, 105, 0.1);
}

.form-help {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

.btn-primary {
    background: #475569;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn-primary:hover {
    background: #334155;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-danger {
    background: #dc2626;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn-danger:hover {
    background: #b91c1c;
}

.btn-premium {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.btn-premium:hover {
    transform: translateY(-1px);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.status-item {
    text-align: center;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
}

.status-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    font-weight: 500;
    margin-bottom: 4px;
}

.status-value {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.upgrade-section {
    border-top: 1px solid #e5e7eb;
    padding-top: 20px;
    text-align: center;
}

.upgrade-section h4 {
    color: #1f2937;
    margin-bottom: 8px;
}

.upgrade-section p {
    color: #6b7280;
    margin-bottom: 16px;
}

.danger-zone .settings-card {
    border-color: #fecaca;
    background: #fef2f2;
}

.danger-content h4 {
    color: #dc2626;
    margin-bottom: 8px;
}

.danger-content p {
    color: #7f1d1d;
    margin-bottom: 16px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    margin: 0;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px 24px;
    border-top: 1px solid #e5e7eb;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
}

/* Alert Styles */
.alert {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-weight: 500;
}

.alert-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-error {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.alert-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
}

.alert-close:hover {
    opacity: 1;
}

/* Responsive Design for Account Settings */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .form-actions {
        flex-direction: column;
    }

    .status-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* Enhanced Suggestions Styles */
.enhanced-suggestions-container {
    margin-top: 20px;
}

.recommendation-section {
    margin-bottom: 30px;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.recommendation-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #e5e7eb;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.recommendation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.recommendation-card.priority-high {
    border-left-color: #dc2626;
    background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.recommendation-card.priority-medium {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.recommendation-card.priority-low {
    border-left-color: #10b981;
    background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.priority-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.priority-badge .priority-icon {
    font-size: 14px;
}

.recommendation-card.priority-high .priority-badge {
    background: #fee2e2;
    color: #dc2626;
}

.recommendation-card.priority-medium .priority-badge {
    background: #fef3c7;
    color: #d97706;
}

.recommendation-card.priority-low .priority-badge {
    background: #d1fae5;
    color: #059669;
}

.impact-score {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    background: #f3f4f6;
    padding: 4px 8px;
    border-radius: 12px;
}

.recommendation-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.recommendation-description {
    color: #6b7280;
    margin-bottom: 16px;
    line-height: 1.5;
}

.keyword-highlight {
    margin-bottom: 16px;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.keyword-tag {
    background: #475569;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.action-items {
    margin-bottom: 16px;
}

.action-items h5 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.action-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.action-list li {
    padding: 6px 0;
    padding-left: 20px;
    position: relative;
    color: #4b5563;
    line-height: 1.4;
}

.action-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.example-phrases, .placement-suggestions {
    margin-bottom: 16px;
}

.example-phrases h5, .placement-suggestions h5 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.phrase-tags, .placement-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.phrase-tag {
    background: #e0e7ff;
    color: #3730a3;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.placement-tag {
    background: #ecfdf5;
    color: #065f46;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.urgency-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.urgency-label {
    color: #6b7280;
    font-weight: 500;
}

.urgency-value {
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.urgency-immediate {
    background: #fee2e2;
    color: #dc2626;
}

.urgency-moderate {
    background: #fef3c7;
    color: #d97706;
}

.urgency-low {
    background: #d1fae5;
    color: #059669;
}

.implement-btn {
    background: #475569;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.implement-btn:hover {
    background: #334155;
}

.implement-btn:disabled {
    background: #10b981;
    cursor: not-allowed;
}

.keyword-summary-container {
    margin-bottom: 30px;
}

.keyword-summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.keyword-summary-card h3 {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 16px;
    border-radius: 8px;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.no-suggestions {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.no-suggestions-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.no-suggestions h3 {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

/* Responsive Design for Enhanced Suggestions */
@media (max-width: 768px) {
    .recommendations-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .recommendation-card {
        padding: 16px;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .card-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .phrase-tags, .placement-tags {
        flex-direction: column;
        align-items: flex-start;
    }
}

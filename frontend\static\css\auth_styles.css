/* Resume Doctor.Ai - Authentication Pages Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 100%);
    background-attachment: fixed;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #334155;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(71, 85, 105, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(100, 116, 139, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(148, 163, 184, 0.04) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.auth-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: 40px;
    width: 100%;
    max-width: 420px;
    box-shadow: 
        0 10px 25px rgba(71, 85, 105, 0.15),
        0 4px 10px rgba(71, 85, 105, 0.1),
        0 0 0 1px rgba(148, 163, 184, 0.1);
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(71, 85, 105, 0.2), transparent);
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.auth-header .subtitle {
    font-size: 14px;
    color: #6b7280;
    font-weight: 400;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: #f9fafb;
}

.form-control:focus {
    outline: none;
    border-color: #475569;
    background: white;
    box-shadow: 0 0 0 3px rgba(71, 85, 105, 0.1);
}

/* Button Styles */
.btn {
    width: 100%;
    padding: 14px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    margin-bottom: 12px;
}

.btn-primary {
    background: linear-gradient(135deg, #475569 0%, #64748b 50%, #94a3b8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(71, 85, 105, 0.25);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(71, 85, 105, 0.35);
    background: linear-gradient(135deg, #64748b 0%, #94a3b8 50%, #cbd5e1 100%);
}

.btn-secondary {
    background: transparent;
    color: #475569;
    border: 2px solid #cbd5e1;
}

.btn-secondary:hover {
    background: #475569;
    color: white;
    border-color: #475569;
    transform: translateY(-2px);
}

/* Links */
.auth-link {
    color: #475569;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.auth-link:hover {
    color: #334155;
    text-decoration: underline;
}

.auth-footer {
    text-align: center;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
    color: #6b7280;
    font-size: 14px;
}

/* Alert Messages */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

/* Loading State */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
    .auth-container {
        margin: 10px;
        padding: 30px 20px;
        max-width: none;
    }
    
    .auth-header h1 {
        font-size: 22px;
    }
    
    .form-control {
        padding: 14px 16px;
    }
}
